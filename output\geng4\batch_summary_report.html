<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量处理汇总报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 5px 0;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .section-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .tasks-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 0.9em;
        }
        .tasks-table th {
            background: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85em;
            white-space: nowrap;
        }
        .tasks-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: top;
        }
        .tasks-table tr:hover {
            background: #f8f9fa;
        }
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        .task-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        .task-link:hover {
            text-decoration: underline;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }
        .error-details {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.9em;
            color: #c53030;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 3px solid #3498db;
        }
        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.85em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 报告头部 -->
        <div class="header">
            <h1>📊 批量处理汇总报告</h1>
            <div class="subtitle">
                车路协同轨迹匹配系统 - 批量分析结果
            </div>
            <div class="subtitle">
                生成时间: 2025-08-30 19:29:54 | 处理任务: 1 个
            </div>
        </div>

        <div class="content">
            <!-- 总体统计 -->
            <div class="section">
                <h2 class="section-title">📈 总体统计</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">1</div>
                        <div class="metric-label">总任务数</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">1</div>
                        <div class="metric-label">成功任务</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0</div>
                        <div class="metric-label">失败任务</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">100.0%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2.03s</div>
                        <div class="metric-label">总耗时</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2.03s</div>
                        <div class="metric-label">平均耗时</div>
                    </div>
                </div>

                <!-- 进度条 -->
                <div>
                    <strong>处理进度:</strong>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100.0%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; color: #7f8c8d;">
                        1/1 任务完成
                    </div>
                </div>
            </div>

            <!-- 任务详情表格 -->
            <div class="section">
                <h2 class="section-title">📋 任务详情</h2>
                <div class="table-container">
                    <table class="tasks-table">
                    <thead>
                        <tr>
                            <th>任务ID</th>
                            <th>感知文件</th>
                            <th>RTK文件</th>
                            <th>状态</th>
                            <th>耗时(秒)</th>
                            <th>匹配点数</th>
                            <th>覆盖率</th>
                            <th>位置精度</th>
                            <th>速度精度</th>
                            <th>航向精度</th>
                            <th>异常统计</th>
                            <th>详细报告</th>
                        </tr>
                    </thead>
                    <tbody>
                        
                        <tr>
                            <td><strong>task_001</strong></td>
                            <td>save.txt</td>
                            <td>NL.dat</td>
                            <td>
                                <span class="status-indicator status-success">
                                    成功
                                </span>
                            </td>
                            <td>2.03</td>
                            <td>18507</td>
                            <td>0.0%</td>
                            <td>N/A</td>
                            <td>N/A</td>
                            <td>N/A</td>
                            <td>
                                
                                <div style="font-size: 0.85em;">
                                    分裂: 0 | 切换: 0<br>
                                    漏检: 0 (0.0s)
                                </div>
                                
                            </td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                    
                                    
                                    <a href="save_vs_NL/NL_save_diagnostic.json" class="task-link" target="_blank">
                                        📄 JSON数据
                                    </a>
                                    
                                    
                                </div>
                            </td>
                        </tr>
                        
                        
                    </tbody>
                    </table>
                </div>
            </div>

            <!-- 性能分析 -->
            
            <div class="section">
                <h2 class="section-title">⚡ 性能分析</h2>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-value">2.03s</div>
                        <div class="stat-label">最快任务</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2.03s</div>
                        <div class="stat-label">最慢任务</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2.03s</div>
                        <div class="stat-label">中位耗时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">29.6</div>
                        <div class="stat-label">任务/分钟</div>
                    </div>
                </div>
            </div>
            

            <!-- 失败任务汇总 -->
            
        </div>

        <!-- 报告尾部 -->
        <div class="footer">
            <div>车路协同轨迹匹配系统 - 批量处理模块</div>
            <div style="margin-top: 5px; opacity: 0.8;">
                报告生成时间: 2025-08-30 19:29:54 | 版本: v1.0
            </div>
        </div>
    </div>
</body>
</html>