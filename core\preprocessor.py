#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始数据预处理器
支持NMEA格式RTK数据和JSON格式感知数据的自动预处理
支持GP和GN前缀的NMEA语句（$GPGGA/$GPRMC和$GNGGA/$GNRMC）
"""

import json
import csv
import re
import os
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RawDataPreprocessor:
    """原始数据预处理器"""
    
    def __init__(self):
        pass
    
    def detect_file_format(self, file_path: str) -> str:
        """
        检测文件格式
        Returns:
            'nmea': NMEA格式RTK数据
            'json': JSON格式感知数据
            'csv': 已处理的CSV格式
            'unknown': 未知格式
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 读取前几行来检测格式
                lines = []
                for _ in range(10):  # 检查前10行
                    line = f.readline()
                    if not line:
                        break
                    lines.append(line.strip())

                # 检查是否是CSV格式（必须是第一行且包含列名）
                for line in lines:
                    if line and line.count(',') > 3:  # CSV应该有多个逗号分隔的列
                        # 检查是否是CSV头部行
                        if ('timestamp' in line and ('lat' in line or 'latitude' in line)
                            and not line.startswith('{') and not line.startswith('[')):
                            return 'csv'

                # 检查是否是NMEA格式（可能包含时间戳标记）
                # 支持 GP 和 GN 前缀
                nmea_count = 0
                for line in lines:
                    if line.startswith('$GP') or line.startswith('$GN'):
                        nmea_count += 1
                        if nmea_count >= 2:  # 找到至少2条NMEA语句
                            return 'nmea'

                # 检查是否是JSON格式
                for line in lines:
                    if line.strip():
                        try:
                            # 清理可能的空字符和其他控制字符
                            clean_line = line.strip().replace('\x00', '').replace('\r', '')
                            if clean_line.startswith('{') and clean_line.endswith('}'):
                                json.loads(clean_line)
                                return 'json'
                        except:
                            continue

        except Exception as e:
            logger.warning(f"检测文件格式失败: {e}")

        return 'unknown'
    
    def parse_nmea_time(self, time_str: str, date_str: str) -> datetime:
        """解析NMEA时间格式"""
        try:
            hours = int(time_str[:2])
            minutes = int(time_str[2:4])
            seconds = float(time_str[4:])
            
            day = int(date_str[:2])
            month = int(date_str[2:4])
            year = 2000 + int(date_str[4:])
            
            dt = datetime(year, month, day, hours, minutes, int(seconds), 
                         int((seconds % 1) * 1000000), timezone.utc)
            return dt
        except:
            return None
    
    def parse_nmea_coordinate(self, coord_str: str, direction: str) -> float:
        """解析NMEA坐标格式"""
        try:
            if not coord_str:
                return None
                
            dot_pos = coord_str.find('.')
            if dot_pos == -1:
                return None
                
            if dot_pos >= 4:  # 经度格式: DDDMM.MMMMMM
                degrees = int(coord_str[:dot_pos-2])
                minutes = float(coord_str[dot_pos-2:])
            else:  # 纬度格式: DDMM.MMMMMM
                degrees = int(coord_str[:dot_pos-2])
                minutes = float(coord_str[dot_pos-2:])
                
            decimal_degrees = degrees + minutes / 60.0
            
            if direction in ['S', 'W']:
                decimal_degrees = -decimal_degrees
                
            return decimal_degrees
        except:
            return None
    
    def process_nmea_data(self, file_path: str) -> List[Dict[str, Any]]:
        """处理NMEA格式RTK数据"""
        logger.info(f"处理NMEA数据文件: {file_path}")

        rtk_data = []

        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        gga_data = {}

        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue

            # 跳过时间戳标记行（格式如：[2025-07-24 19:20:42.513]# RECV ASCII>）
            if line.startswith('[') and ']#' in line:
                continue

            try:
                # 支持 GP 和 GN 前缀的 GGA 语句
                if line.startswith('$GPGGA') or line.startswith('$GNGGA'):
                    parts = line.split(',')
                    if len(parts) >= 15:
                        time_str = parts[1]
                        lat_str = parts[2]
                        lat_dir = parts[3]
                        lon_str = parts[4]
                        lon_dir = parts[5]
                        quality = parts[6]
                        num_sats = parts[7]
                        hdop = parts[8]
                        altitude = parts[9]

                        if time_str and lat_str and lon_str:
                            gga_data[time_str] = {
                                'time': time_str,
                                'latitude': self.parse_nmea_coordinate(lat_str, lat_dir),
                                'longitude': self.parse_nmea_coordinate(lon_str, lon_dir),
                                'quality': quality,
                                'num_sats': num_sats,
                                'hdop': hdop,
                                'altitude': altitude
                            }

                # 支持 GP 和 GN 前缀的 RMC 语句
                elif line.startswith('$GPRMC') or line.startswith('$GNRMC'):
                    parts = line.split(',')
                    if len(parts) >= 10:
                        time_str = parts[1]
                        status = parts[2]
                        date_str = parts[9]
                        speed_knots = parts[7]
                        course = parts[8]

                        if time_str in gga_data and date_str and status in ['A', 'D']:
                            gga_info = gga_data[time_str]

                            dt_utc = self.parse_nmea_time(time_str, date_str)
                            if dt_utc:
                                speed_ms = float(speed_knots) * 0.514444 if speed_knots else 0.0

                                rtk_data.append({
                                    'timestamp': dt_utc.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                                    'lat': gga_info['latitude'],
                                    'lon': gga_info['longitude'],
                                    'speed': speed_ms,
                                    'heading': float(course) if course else 0.0,
                                    'altitude': float(gga_info['altitude']) if gga_info['altitude'] else 0.0,
                                    'quality': int(gga_info['quality']) if gga_info['quality'] else 0,
                                    'num_sats': int(gga_info['num_sats']) if gga_info['num_sats'] else 0,
                                    'hdop': float(gga_info['hdop']) if gga_info['hdop'] else 0.0
                                })

                            del gga_data[time_str]

                # 忽略其他NMEA语句（如$GPCHC、$GNCHC等）

            except Exception as e:
                logger.warning(f"处理第{line_num}行时出错: {e}")
                continue

        logger.info(f"NMEA数据处理完成，共{len(rtk_data)}条记录")
        return rtk_data
    
    def process_json_data(self, file_path: str) -> List[Dict[str, Any]]:
        """处理JSON格式感知数据"""
        logger.info(f"处理JSON数据文件: {file_path}")
        
        perception_data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue

            try:
                # 清理可能的空字符和其他控制字符
                clean_line = line.replace('\x00', '').replace('\r', '')
                data = json.loads(clean_line)

                # 支持新格式和旧格式
                if 'timestamp' in data and 'object_result' in data:
                    # 新格式：毫秒时间戳（UTC时间戳，但按北京时间处理）
                    timestamp_ms = data['timestamp']
                    beijing_tz = timezone(timedelta(hours=8))  # 北京时间 UTC+8
                    timestamp_dt = datetime.fromtimestamp(timestamp_ms / 1000.0, beijing_tz)
                    timestamp_str = timestamp_dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                    obj_list = data['object_result']
                    logger.debug(f"处理新格式数据，时间戳: {timestamp_str}, 对象数量: {len(obj_list)}")
                elif 'Timestamp' in data and 'Obj_List' in data:
                    # 旧格式
                    timestamp_str = data['Timestamp']
                    obj_list = data['Obj_List']
                else:
                    logger.warning(f"第{line_num}行：未识别的JSON格式，跳过")
                    continue

                for obj in obj_list:
                    # 支持新格式和旧格式的对象数据
                    logger.debug(f"检查对象字段: {list(obj.keys())[:10]}")
                    if 'id' in obj and 'lat' in obj and 'lon' in obj:
                        # 新格式：直接使用字段值，速度已经是m/s
                        logger.debug(f"处理新格式对象: {obj.get('id', 'unknown')}")
                        try:
                            perception_data.append({
                                'timestamp': timestamp_str,
                                'id': str(obj['id']),
                                'lat': float(obj['lat']),
                                'lon': float(obj['lon']),
                                'speed': float(obj.get('speed', 0.0))/3.6,  # 新格式也是km/h
                                'heading': float(obj.get('heading', 0.0)),
                                'altitude': float(obj.get('ele', 0.0)),
                                'vehicle_class': int(obj.get('veh_type', 0)),
                                'ptc_type': int(obj.get('type', 1)),
                                'vehicle_height': float(obj.get('height', 0.0)),
                                'vehicle_width': float(obj.get('width', 0.0)),
                                'vehicle_length': float(obj.get('length', 0.0))
                            })
                        except Exception as obj_e:
                            logger.warning(f"处理新格式对象时出错: {obj_e}, 对象: {obj}")
                            continue
                    elif all(key in obj for key in ['ID', 'PtcLat', 'PtcLon']):
                        # 旧格式：转换速度单位：km/h -> m/s
                        try:
                            speed_kmh = float(obj.get('PtcSpeed', 0.0))
                            speed_ms = speed_kmh / 3.6  # km/h 转换为 m/s

                            perception_data.append({
                                'timestamp': timestamp_str,
                                'id': str(obj['ID']),
                                'lat': float(obj['PtcLat']),
                                'lon': float(obj['PtcLon']),
                                'speed': speed_ms,
                                'heading': float(obj.get('PtcHeading', 0.0)),
                                'altitude': float(obj.get('PtcEle', 0.0)),
                                'vehicle_class': int(obj.get('vehicleClass', 0)),
                                'ptc_type': int(obj.get('PtcType', 1)),
                                'vehicle_height': float(obj.get('VehH', 0.0)),
                                'vehicle_width': float(obj.get('VehW', 0.0)),
                                'vehicle_length': float(obj.get('VehL', 0.0))
                            })
                        except Exception as obj_e:
                            logger.warning(f"处理旧格式对象时出错: {obj_e}, 对象: {obj}")
                            continue
                            
            except Exception as e:
                logger.warning(f"处理第{line_num}行时出错: {e}")
                logger.debug(f"问题行内容: {line[:100]}...")
                continue
        
        logger.info(f"JSON数据处理完成，共{len(perception_data)}条记录")
        return perception_data
    
    def save_to_csv(self, data: List[Dict[str, Any]], output_path: str, fieldnames: List[str]):
        """保存数据到CSV文件"""
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        logger.info(f"数据已保存到: {output_path}")
    
    def preprocess_rtk_file(self, input_path: str, output_path: str = None) -> str:
        """
        预处理RTK文件
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径，如果为None则自动生成
        Returns:
            输出文件路径
        """
        if output_path is None:
            input_stem = Path(input_path).stem
            output_path = f"{input_stem}_processed.csv"
        
        file_format = self.detect_file_format(input_path)
        
        if file_format == 'csv':
            logger.info(f"RTK文件已是CSV格式，直接复制: {input_path}")
            # 检查是否需要列名转换
            import pandas as pd
            df = pd.read_csv(input_path)
            if 'latitude' in df.columns:
                df = df.rename(columns={'latitude': 'lat', 'longitude': 'lon'})
            
            required_columns = ['timestamp', 'lat', 'lon', 'speed', 'heading']
            df = df[required_columns]
            df.to_csv(output_path, index=False)
            return output_path
            
        elif file_format == 'nmea':
            rtk_data = self.process_nmea_data(input_path)
            if rtk_data:
                fieldnames = ['timestamp', 'lat', 'lon', 'speed', 'heading', 'altitude', 'quality', 'num_sats', 'hdop']
                # 只保存必要字段
                required_fieldnames = ['timestamp', 'lat', 'lon', 'speed', 'heading']
                filtered_data = []
                for record in rtk_data:
                    filtered_record = {field: record.get(field, 0) for field in required_fieldnames}
                    filtered_data.append(filtered_record)
                
                self.save_to_csv(filtered_data, output_path, required_fieldnames)
                return output_path
            else:
                raise ValueError("RTK数据处理失败")
        else:
            raise ValueError(f"不支持的RTK文件格式: {file_format}")
    
    def preprocess_perception_file(self, input_path: str, output_path: str = None) -> str:
        """
        预处理感知文件
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径，如果为None则自动生成
        Returns:
            输出文件路径
        """
        if output_path is None:
            input_stem = Path(input_path).stem
            output_path = f"{input_stem}_processed.csv"
        
        file_format = self.detect_file_format(input_path)
        
        if file_format == 'csv':
            logger.info(f"感知文件已是CSV格式，直接复制: {input_path}")
            # 检查是否需要列名转换
            import pandas as pd
            df = pd.read_csv(input_path)
            if 'latitude' in df.columns:
                df = df.rename(columns={'latitude': 'lat', 'longitude': 'lon'})
            if 'object_id' in df.columns:
                df = df.rename(columns={'object_id': 'id'})
            
            required_columns = ['timestamp', 'id', 'lat', 'lon', 'speed', 'heading']
            df = df[required_columns]
            df.to_csv(output_path, index=False)
            return output_path
            
        elif file_format == 'json':
            perception_data = self.process_json_data(input_path)
            if perception_data:
                # 只保存必要字段
                required_fieldnames = ['timestamp', 'id', 'lat', 'lon', 'speed', 'heading']
                filtered_data = []
                for record in perception_data:
                    filtered_record = {field: record.get(field, 0) for field in required_fieldnames}
                    filtered_data.append(filtered_record)
                
                self.save_to_csv(filtered_data, output_path, required_fieldnames)
                return output_path
            else:
                raise ValueError("感知数据处理失败")
        else:
            raise ValueError(f"不支持的感知文件格式: {file_format}")
    
    def preprocess_files(self, rtk_path: str, perception_path: str, 
                        output_dir: str = None) -> Tuple[str, str]:
        """
        批量预处理RTK和感知文件
        Args:
            rtk_path: RTK文件路径
            perception_path: 感知文件路径
            output_dir: 输出目录，如果为None则使用当前目录
        Returns:
            (rtk_output_path, perception_output_path)
        """
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            rtk_output = os.path.join(output_dir, "rtk.csv")
            perception_output = os.path.join(output_dir, "car_all.csv")
        else:
            rtk_output = "rtk.csv"
            perception_output = "car_all.csv"
        
        logger.info("开始批量预处理...")
        
        # 预处理RTK文件
        rtk_processed = self.preprocess_rtk_file(rtk_path, rtk_output)
        
        # 预处理感知文件
        perception_processed = self.preprocess_perception_file(perception_path, perception_output)
        
        logger.info("批量预处理完成")
        return rtk_processed, perception_processed

def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='原始数据预处理器')
    parser.add_argument('--rtk', required=True, help='RTK数据文件路径')
    parser.add_argument('--perception', required=True, help='感知数据文件路径')
    parser.add_argument('--output-dir', help='输出目录')
    
    args = parser.parse_args()
    
    preprocessor = RawDataPreprocessor()
    
    try:
        rtk_output, perception_output = preprocessor.preprocess_files(
            args.rtk, args.perception, args.output_dir
        )
        
        print(f"✅ 预处理完成!")
        print(f"RTK输出: {rtk_output}")
        print(f"感知输出: {perception_output}")
        
    except Exception as e:
        print(f"❌ 预处理失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 